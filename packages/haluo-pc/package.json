{"name": "@haluo/pc", "version": "0.0.11-alpha.6", "main": "./dist/haluo-pc.umd.js", "module": "./dist/haluo-pc.es.js", "typings": "dist/src/index.d.ts", "scripts": {"dev": "vite --open", "build": "vite build --config vite.config.build.ts", "build:types": "", "serve": "vite preview", "test": "jest"}, "dependencies": {"@types/node": "^16.7.2", "@types/qs": "^6.9.7", "axios": "^0.21.1", "element-plus": "^1.1.0-beta.6", "jest": "^26", "qs": "^6.10.1", "sass": "^1.38.1", "vue": "^3.0.5", "vue-router": "4"}, "license": "MIT", "keywords": ["vue", "vue3", "ts", "vite", "haluo"], "files": ["dist", "README.md", "package.json", "LICENSE"], "devDependencies": {"@types/jest": "^26", "@types/node": "^16.7.2", "@vitejs/plugin-vue": "^4.0.0", "@vue/compiler-sfc": "^3.0.5", "@vue/test-utils": "^2.0.0-rc.14", "flush-promises": "^1.0.2", "ts-jest": "^26", "ts-node": "^10.2.1", "typescript": "^4.3.2", "vite": "^4.0.0", "vite-plugin-dts": "^0.9.3", "vue-jest": "^5.0.0-alpha.10", "vue-tsc": "^0.3.0"}, "publishConfig": {"access": "public"}, "gitHead": "af269622a5c9edfb2b839a8311b99b782f53abec"}