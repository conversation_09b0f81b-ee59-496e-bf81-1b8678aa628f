import { App } from 'vue'
import CascadeArea from './components/cascade-area'
import Pagination from './components/pagination'
import Sticky from './components/sticky'
const components = [CascadeArea, Pagination, Sticky]

const install: any = function (app: App) {
  if (install.installed) return
  install.installed = true
  components.map(component => app.component(component.name, component))
  // components.map(component => Vue.use(component))
}

export default {
  install,
  ...components
}
