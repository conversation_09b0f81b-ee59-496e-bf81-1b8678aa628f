<template>
  <div class="tools" v-if="editor.fontInfo">
    <div class="tools-content">
      <el-tooltip class="item" effect="dark" content="撤回" placement="top">
        <img id="undo" :style="!backState.undo ? {transform: 'rotateY(180deg)'}:''" :src="backState.undo ? getImageUrl('<EMAIL>') : getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="back($event)">
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="反撤回" placement="top">
        <img id="redo" :style="backState.redo ? {transform: 'rotateY(180deg)'}:''" :src="backState.redo ? getImageUrl('<EMAIL>') : getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="forward($event)">
      </el-tooltip>
      <el-divider class="m12" direction="vertical"></el-divider>
      <el-tooltip class="item" effect="dark" content="清除格式" placement="top">
        <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="clearFormat">
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="格式刷" placement="top">
        <div class="box">
          <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="copyFormat">
          <div v-if="editor.cursorStyle !== 'auto'" class="box-mask"></div>
        </div>
      </el-tooltip>
      <el-divider class="m12" direction="vertical"></el-divider>
      <!-- <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt="" >
    <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt="" > -->
      <!-- <el-divider direction="vertical"></el-divider> -->
      <el-popover
        :visible="popoverVisibleFont"
        placement="bottom"
        width="152"
        
      >
        <template #reference>
          <div class="tools-title">
            <el-tooltip class="item" effect="dark" content="字号" placement="top">
              <div ref="font" :class="[ editor.setAlignFlag ? '' : 'disable', 'tools-title']" @click="showDialog('popoverVisibleFont')">
                <span>{{ activeFontsize }}</span>
                <img :src="getImageUrl('<EMAIL>')" width="16" height="40" alt="" >
              </div>
            </el-tooltip>
          </div>
        </template>
        <template #default>
          <ul v-click-outside="handlePopover" class="tools-font">
          <li v-for="(item, index) in fontsizes" :class="[(activeFontsize + 'px') === item.value ? 'active' : '']" :key="index" data-editor-id="setFontSize" @click="setFont($event, item.value)">
            <span :style="{fontSize:item.value}">{{ item.name }}</span>
            <span style="font-size: 10px;">{{item.desc}}</span>
          </li>
        </ul>
        </template>
      </el-popover>
      <el-divider class="ml16 mr12" direction="vertical"></el-divider>
      <el-tooltip class="item" effect="dark" content="标题" placement="top">
        <div class="box">
          <img :src="getImageUrl('<EMAIL>')" data-editor-id="makeHeader" width="40" height="40" alt=""  @click="setTitle($event, statusInfo.title ? 0 : 1)">
          <div v-if="statusInfo && statusInfo.title" class="box-mask"></div>
        </div>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="加粗" placement="top">
        <div class="box">
          <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="setStyle(statusInfo.bold?'removeBold':'bold', 'bold', !statusInfo.bold)">
          <div v-if="statusInfo && statusInfo.bold" class="box-mask"></div>
        </div>
      </el-tooltip>
      <div>
        <el-popover
          placement="bottom"
          :visible="popoverVisibleList"
          width="152"
          
        >
          <template #reference>
            <div class="tools-title">
              <el-tooltip class="item" effect="dark" content="列表" placement="top">
                <div ref="list" :class="[ editor.setAlignFlag ? '' : 'disable']" @click="showDialog('popoverVisibleList')">
                  <img :src="getImageUrl('<EMAIL>')" width="24" height="40" alt="" >
                  <img :src="getImageUrl('<EMAIL>')" width="16" height="40" alt="" >
                </div>
              </el-tooltip>
            </div>
          </template>
          <template #default>
            <ul v-click-outside="handlePopover" class="tools-title_content">
              <li data-editor-id="makeHeader" @click="updatePosition(() => {
                setStyle('makeUnorderedList')
              })">无序列表</li>
              <li data-editor-id="makeHeader" @click="updatePosition(() => {
                setStyle('makeOrderedList')
              })">有序列表</li>
            </ul>
          </template>
        </el-popover>
      </div>
      <el-tooltip class="item" effect="dark" content="倾斜" placement="top">
        <div class="box">
          <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="setStyle(statusInfo.italic?'removeItalic':'italic', 'italic', !statusInfo.italic)">
          <div v-if="statusInfo && statusInfo.italic" class="box-mask"></div>
        </div>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="下划线" placement="top">
        <div class="box">
          <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="setStyle(statusInfo.underline?'removeUnderline':'underline', 'underline',!statusInfo.underline)">
          <div v-if="statusInfo && statusInfo.underline" class="box-mask"></div>
        </div>
      </el-tooltip>
      <el-tooltip v-if="!hidden(ToolsEnum.LINK)" class="item" effect="dark" content="链接" placement="top">
        <div class="box">
          <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="insertLink">
        </div>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="首行缩进" placement="top">
        <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="setStyle('setIndent')">
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="文字颜色" placement="top">
        <div class="wrap" @click="selectColorPicker">
          <img :src="getImageUrl('<EMAIL>')" width="24" height="40" alt="" >
          <img :src="getImageUrl('<EMAIL>')" width="16" height="40" alt="" >
          <colorPicker ref="colorPicker" v-model="color" @change="setColor" />
        </div>
      </el-tooltip>

      <el-divider class="m12" direction="vertical"></el-divider>
      <el-popover
        :visible="popoverVisible"
        placement="bottom"
        width="152"
        
      >
        <template #reference>
          <div class="tools-title">
            <el-tooltip class="item" effect="dark" content="对齐方式" placement="top">
              <div ref="align" :class="[ editor.setAlignFlag ? '' : 'disable']" @click="showPopover">
                <img :src="getImageUrl('<EMAIL>')" width="24" height="40" alt="" >
                <img :src="getImageUrl('<EMAIL>')" width="16" height="40" alt="" >
              </div>
            </el-tooltip>
          </div>
        </template>
        <template #default>
          <ul v-click-outside="handlePopover" class="tools-title_content">
            <li v-for="(item, index) in alignMethods" :class="[activeAlign === item.value ? 'active' : '']" :key="index" data-editor-id="setTextAlignment" @click="setAlign($event, item.value)">{{ item.name }}</li>
          </ul>
        </template>
      </el-popover>
      <el-divider class="ml16 mr12" direction="vertical"></el-divider>
      <el-popover
        :visible="popoverVisibleImg"
        v-if="!hidden(ToolsEnum.INSERT_IMAGE)"
        placement="bottom"
        width="180"
        
      >
        <template #reference>
          <div class="tools-title">
            <el-tooltip class="item" effect="dark" content="插入图片" placement="top">
              <img ref="img" :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="setVisibleImg">
            </el-tooltip>
          </div>
        </template>
        <template #default>
          <ul v-click-outside="handlePopover" class="tools-title_content" style="padding: 0">
            <li  :class="[activeImgType === 'normal' ? 'active' : '']"  @click="insertImg('normal')">{{ '上传图片' }}</li>
            <li  :class="[activeImgType === 'seamless' ? 'active' : '']"  @click="insertImg('seamless')">{{ '上传无缝拼接图片' }}</li>
          </ul>
        </template>
      </el-popover>
      <el-tooltip v-if="!hidden(ToolsEnum.INSERT_VIDEO)" class="item" effect="dark" content="插入视频" placement="top">
        <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="insertVideo">
      </el-tooltip>
      <el-tooltip v-if="!isOss && !hidden(ToolsEnum.INSERT_ARTICLE)" class="item" effect="dark" content="插入文章/视频" placement="top">
        <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="insertArticle">
      </el-tooltip>
      <el-tooltip v-if="!hidden(ToolsEnum.COLLECT_ARTICLE)" class="item" effect="dark" content="采集文章" placement="top">
        <img :src="getImageUrl('<EMAIL>')" width="40" height="40" alt=""  @click="collectArticle">
      </el-tooltip>
    </div>
  </div>
</template>
<script>
import { ElTooltip, ElDivider, ElPopover } from 'element-plus'
import { ToolsEnum } from './ToolsEnum.js'
import vColorPicker from './vcolorpicker/vcolorpicker.js'
import './vcolorpicker/style.css'
export default {
  components: {
    ElTooltip,
    ElDivider,
    ElPopover,
    colorPicker: vColorPicker.colorPicker
  },
  props: ['editor', 'isOss','hiddenTools'],
  data() {
    return {
      ToolsEnum,
      color: '#000000',
      popoverVisibleFont: false,
      headType: 0,
      popoverVisible: false,
      showToolsArticle: true,
      popoverVisibleList: false,
      popoverVisibleImg: false,
      activeImgType: false,
      activeAlign: '',
      alignMethods: [
        { value: 'left', name: '左对齐' },
        { value: 'right', name: '右对齐' },
        { value: 'center', name: '居中对齐' },
        { value: 'justify', name: '两端对齐' },
      ],
      activeFontsize: '17',
      fontsizes: [
        { value: '17px', name: '17', desc: '默认正文字号' },
        { value: '18px', name: '18', desc: '默认标题字号' },
        { value: '19px', name: '19' },
        { value: '20px', name: '20' },
        { value: '24px', name: '24' },
        { value: '32px', name: '32' }
      ],
      statusInfo: {
        bold: false,
        italic: false,
        underline: false
      },
      backState: {
        redo: false,
        undo: false
      }
    }
  },
  computed: {
    fontInfo() {
      if(this.editor.fontInfo) {
        const info = JSON.parse(JSON.stringify(this.editor.fontInfo))
        info.size = parseInt(info.size)
        if (info.size === 100 || !info.size) {
          info.size = 17
        }
        return info
      }
      return {
        size: ''
      }
    },
    styleStatus() {
      return this.editor.styleStatus
    },
    doState() {
      return this.editor.backState
    },
    hidden() {
      return function(val) { 
        return this.hiddenTools && this.hiddenTools.includes(val)
      }
    }
  },
  watch: {
    'fontInfo.size'(val) {
      this.activeFontsize = val
    },
    styleStatus: {
      handler(val) {
        this.statusInfo = val
      },
      immediate: true
    },
    doState: {
      handler(val) {
        this.backState = val
      },
      immediate: true
    }
  },
  mounted() {
    if(!this.isOss) {
      const user = JSON.parse(window.localStorage.getItem('user') || '{}');
      // const certifyList = user.certifyList.map(item => { return item.type }) || []
      // this.showToolsArticle = certifyList.includes('11')
    }
  },
  methods: {
    getImageUrl(name) {
      return new URL(`../assets/img/tools/${name}`, import.meta.url).href
    },
    selectColorPicker(){
      // fix 点击富文本选中丢失
      const wrap = this.$refs['colorPicker'].$el
      const btn = wrap.querySelector('.colorBtn')
      btn.click()
    },
    insertLink() {
      this.editor.showLink()
    },
    clearFormat() {
      this.editor.clearFormat()
    },
    setColor(val) {
      console.log('文字颜色')
      this.updatePosition(() => {

        this.editor.setColor(val)
      })
    },
    setStyle(type, key, flag) {
      this.popoverVisibleList = false
      if (this.statusInfo[key] !== undefined) {
        this.statusInfo[key] = flag
      }
      this.editor.setStyle(type)
    },
    insertArticle() {
      this.editor.uploadArticle()
    },
    setVisibleImg() {
      this.popoverVisibleImg = true
    },
    insertImg(type) {
      this.activeImgType = type
      this.editor.uploadImg(type)
    },
    insertVideo() {
      this.editor.showUploadVideo()
    },
    collectArticle() {
      this.editor.collectArticle()
    },
    back(e) {
      this.editor.setContent(e)
      this.backState = this.editor.editor.getUndoOrRedoState()
    },
    forward(e) {
      this.editor.setContent(e)
      this.backState = this.editor.editor.getUndoOrRedoState()
    },
    setTitle(e, type) {
      this.headType = type
      this.editor.setContent(e, {
        makeHeader: {
          type
        }
      })
    },
    copyFormat() {
      this.editor.setCursor()
    },
    handlePopover(e) {
      const target = this.$refs['align']
      const font = this.$refs['font']
      const list = this.$refs['list']
      const img = this.$refs['img']
      if (target?.contains(e) || font?.contains(e) || list?.contains(e) || img?.contains(e)) return
      this.popoverVisible = false
      this.popoverVisibleFont = false
      this.popoverVisibleList = false
      this.popoverVisibleImg = false
    },
    // todo 富文本设置样式位置会回到顶部
    updatePosition(fn) {
      const scrollTop = document.documentElement.scrollTop
      fn  && fn()
      setTimeout(() => {
        document.documentElement.scrollTop = scrollTop
      }, 50);

    },
    setAlign(e, type) {
      this.updatePosition(() =>{

        this.editor.setAlign(e, type)
        this.popoverVisible = false
      })
    },
    setFont(e, type) {
      this.activeFontsize = parseInt(type)
      this.editor.setFontSize(e, type)
      this.popoverVisibleFont = false
    },
    showDialog(type) {
      this[type] = true
    },
    showPopover() {
      if (!this.editor.setAlignFlag) return
      this.activeAlign = this.editor.getCursorAlignStyle()
      this.popoverVisible = true
    },
    openDialog(type) {
      this.editor.openDialog(type)
    }
  }
}
</script>
<style lang="less" scoped>
.m12{
  margin: 0 12px;
}
.ml16{
  margin-left: 16px;
}
.mr12{
  margin-right: 12px;
}
.ml12{
  margin-left: 12px;
}
.mr18{
  margin-right: 18px;
}
.tools{
  z-index: 2;
  position: relative;
  background: #FAFAFA;
  .tools-content{
    display: flex;
    align-items: center;
  }
  img{
    cursor: pointer;
  }
  .box{
    position: relative;
    .box-mask{
      width:40px;height:40px;background: black;opacity:0.2; top: 0;position: absolute;
      pointer-events: none;
    }
  }
  .tools-title{
    color: #333333;
    font-size: 18px;
    display: flex;
    align-items: center;
    cursor: pointer;

  }
  .disable{
    cursor: not-allowed;
    filter: blur(0.6px);
    img{
      cursor: not-allowed;
    }
  }
  .wrap{
    position: relative;
  }
  :deep(.m-colorPicker){
    position: absolute;
    width: 100%;
    left: 0;
    z-index: -1;
    height: 100%;
    .colorBtn{
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
    }
  }
}
</style>
<style lang="less">
.tools-title_content{
  padding-left: 30px;
  font-size: 18px;
  li{
    cursor: pointer;
    &.active{
      color: red;
    }
  }
  li~li{
    margin-top: 18px;
  }
}
.tools-font{
  li{
    display: flex;
    align-items: center;
    cursor: pointer;
    &.active{
      span:nth-child(1){
        color: red;
      }
    }
    span:nth-child(2){
      margin-left: 5px;
    }
  }
}
</style>
