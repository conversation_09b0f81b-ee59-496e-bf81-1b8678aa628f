import { getCurrentScope as ne, onScopeDispose as le, unref as w, watch as re, defineComponent as se, ref as B, computed as F, openBlock as k, createElementBlock as h, createCommentVNode as L, createElementVNode as i, normalizeClass as W, normalizeStyle as M, Fragment as V, renderList as N, withDirectives as ue, isRef as ae, vModelText as ie, pushScopeId as ce, popScopeId as de } from "vue";
var j;
const $ = typeof window < "u", fe = (e) => typeof e == "string", pe = () => {
};
$ && ((j = window == null ? void 0 : window.navigator) == null ? void 0 : j.userAgent) && /iP(ad|hone|od)/.test(window.navigator.userAgent);
function ve(e) {
  return typeof e == "function" ? e() : w(e);
}
function me(e) {
  return e;
}
function Ce(e) {
  return ne() ? (le(e), !0) : !1;
}
function A(e) {
  var t;
  const o = ve(e);
  return (t = o == null ? void 0 : o.$el) != null ? t : o;
}
const K = $ ? window : void 0;
$ && window.document;
$ && window.navigator;
$ && window.location;
function Q(...e) {
  let t, o, s, p;
  if (fe(e[0]) || Array.isArray(e[0]) ? ([o, s, p] = e, t = K) : [t, o, s, p] = e, !t)
    return pe;
  Array.isArray(o) || (o = [o]), Array.isArray(s) || (s = [s]);
  const _ = [], I = () => {
    _.forEach((v) => v()), _.length = 0;
  }, C = (v, g, m) => (v.addEventListener(g, m, p), () => v.removeEventListener(g, m, p)), d = re(() => A(t), (v) => {
    I(), v && _.push(...o.flatMap((g) => s.map((m) => C(v, g, m))));
  }, { immediate: !0, flush: "post" }), y = () => {
    d(), I();
  };
  return Ce(y), y;
}
function _e(e, t, o = {}) {
  const { window: s = K, ignore: p, capture: _ = !0, detectIframe: I = !1 } = o;
  if (!s)
    return;
  let C = !0, d;
  const y = (u) => {
    s.clearTimeout(d);
    const a = A(e);
    if (!(!a || a === u.target || u.composedPath().includes(a))) {
      if (!C) {
        C = !0;
        return;
      }
      t(u);
    }
  }, v = (u) => p && p.some((a) => {
    const b = A(a);
    return b && (u.target === b || u.composedPath().includes(b));
  }), g = [
    Q(s, "click", y, { passive: !0, capture: _ }),
    Q(s, "pointerdown", (u) => {
      const a = A(e);
      a && (C = !u.composedPath().includes(a) && !v(u));
    }, { passive: !0 }),
    Q(s, "pointerup", (u) => {
      if (u.button === 0) {
        const a = u.composedPath();
        u.composedPath = () => a, d = s.setTimeout(() => y(u), 50);
      }
    }, { passive: !0 }),
    I && Q(s, "blur", (u) => {
      var a;
      const b = A(e);
      ((a = s.document.activeElement) == null ? void 0 : a.tagName) === "IFRAME" && !(b != null && b.contains(s.document.activeElement)) && t(u);
    })
  ].filter(Boolean);
  return () => g.forEach((u) => u());
}
const D = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {}, R = "__vueuse_ssr_handlers__";
D[R] = D[R] || {};
D[R];
var q;
(function(e) {
  e.UP = "UP", e.RIGHT = "RIGHT", e.DOWN = "DOWN", e.LEFT = "LEFT", e.NONE = "NONE";
})(q || (q = {}));
var ge = Object.defineProperty, z = Object.getOwnPropertySymbols, be = Object.prototype.hasOwnProperty, ye = Object.prototype.propertyIsEnumerable, G = (e, t, o) => t in e ? ge(e, t, { enumerable: !0, configurable: !0, writable: !0, value: o }) : e[t] = o, ke = (e, t) => {
  for (var o in t || (t = {}))
    be.call(t, o) && G(e, o, t[o]);
  if (z)
    for (var o of z(t))
      ye.call(t, o) && G(e, o, t[o]);
  return e;
};
const he = {
  easeInSine: [0.12, 0, 0.39, 0],
  easeOutSine: [0.61, 1, 0.88, 1],
  easeInOutSine: [0.37, 0, 0.63, 1],
  easeInQuad: [0.11, 0, 0.5, 0],
  easeOutQuad: [0.5, 1, 0.89, 1],
  easeInOutQuad: [0.45, 0, 0.55, 1],
  easeInCubic: [0.32, 0, 0.67, 0],
  easeOutCubic: [0.33, 1, 0.68, 1],
  easeInOutCubic: [0.65, 0, 0.35, 1],
  easeInQuart: [0.5, 0, 0.75, 0],
  easeOutQuart: [0.25, 1, 0.5, 1],
  easeInOutQuart: [0.76, 0, 0.24, 1],
  easeInQuint: [0.64, 0, 0.78, 0],
  easeOutQuint: [0.22, 1, 0.36, 1],
  easeInOutQuint: [0.83, 0, 0.17, 1],
  easeInExpo: [0.7, 0, 0.84, 0],
  easeOutExpo: [0.16, 1, 0.3, 1],
  easeInOutExpo: [0.87, 0, 0.13, 1],
  easeInCirc: [0.55, 0, 1, 0.45],
  easeOutCirc: [0, 0.55, 0.45, 1],
  easeInOutCirc: [0.85, 0, 0.15, 1],
  easeInBack: [0.36, 0, 0.66, -0.56],
  easeOutBack: [0.34, 1.56, 0.64, 1],
  easeInOutBack: [0.68, -0.6, 0.32, 1.6]
};
ke({
  linear: me
}, he);
const J = (e) => (ce("data-v-b46fc003"), e = e(), de(), e), we = { class: "hd" }, Oe = { class: "bd" }, Ie = /* @__PURE__ */ J(() => /* @__PURE__ */ i("h3", null, "\u4E3B\u9898\u989C\u8272", -1)), Ee = { class: "tColor" }, Pe = ["onMouseover", "onClick"], Me = { class: "bColor" }, Ae = ["onMouseover", "onClick"], $e = /* @__PURE__ */ J(() => /* @__PURE__ */ i("h3", null, "\u6807\u51C6\u989C\u8272", -1)), Te = { class: "tColor" }, Be = ["onMouseover", "onClick"], Ve = {
  name: "colorPicker"
}, Ne = /* @__PURE__ */ se({
  ...Ve,
  props: {
    modelValue: { type: String, required: !0 },
    defaultColor: { type: String, required: !1, default: "#000000" },
    disabled: { type: Boolean, required: !1 }
  },
  emits: ["update:modelValue", "change"],
  setup(e, { emit: t }) {
    const o = e, s = B(!1), p = () => {
      s.value = !o.disabled;
    }, _ = B(null);
    _e(_, () => {
      s.value = !1;
    });
    const C = B(""), d = (n) => {
      C.value = n;
    }, y = ["#000000", "#ffffff", "#eeece1", "#1e497b", "#4e81bb", "#e2534d", "#9aba60", "#8165a0", "#47acc5", "#f9974c"], v = [
      ["#7f7f7f", "#f2f2f2"],
      ["#0d0d0d", "#808080"],
      ["#1c1a10", "#ddd8c3"],
      ["#0e243d", "#c6d9f0"],
      ["#233f5e", "#dae5f0"],
      ["#632623", "#f2dbdb"],
      ["#4d602c", "#eaf1de"],
      ["#3f3150", "#e6e0ec"],
      ["#1e5867", "#d9eef3"],
      ["#99490f", "#fee9da"]
    ], g = ["#c21401", "#ff1e02", "#ffc12a", "#ffff3a", "#90cf5b", "#00af57", "#00afee", "#0071be", "#00215f", "#72349d"], m = o.modelValue, u = F(() => C.value ? C : a), a = F(() => o.modelValue ? o.modelValue : o.defaultColor), b = F(() => {
      let n = [];
      for (let r of v)
        n.push(oe(r[1], r[0], 5));
      return n;
    }), x = B(null), X = () => {
      var n;
      (n = x.value) == null || n.click();
    }, E = (n) => {
      t("update:modelValue", n), t("change", n), s.value = !1;
    }, Y = () => {
      E(o.defaultColor);
    }, Z = (n) => n.length === 4 ? n = "#" + n[1] + n[1] + n[2] + n[2] + n[3] + n[3] : n, ee = (n, r, l) => {
      const c = (n << 16 | r << 8 | l).toString(16);
      return "#" + new Array(Math.abs(c.length - 7)).join("0") + c;
    }, H = (n) => {
      n = Z(n);
      let r = [];
      for (let l = 1; l < 7; l += 2)
        r.push(parseInt("0x" + n.slice(l, l + 2)));
      return r;
    }, oe = (n, r, l) => {
      let c = H(n), f = H(r), S = (f[0] - c[0]) / l, T = (f[1] - c[1]) / l, te = (f[2] - c[2]) / l, U = [];
      for (let P = 0; P < l; P++)
        U.push(ee(S * P + c[0], T * P + c[1], te * P + c[2]));
      return U;
    };
    return (n, r) => (k(), h("div", {
      class: "m-colorPicker",
      ref_key: "colorPicker",
      ref: _,
      onClick: r[7] || (r[7] = (l) => {
        l.stopPropagation();
      })
    }, [
      L(" \u989C\u8272\u663E\u793A\u5C0F\u65B9\u5757 "),
      i("div", {
        class: W(["colorBtn", { disabled: e.disabled }]),
        style: M(`background-color: ${w(a)}`),
        onClick: p
      }, null, 6),
      L(" \u989C\u8272\u8272\u76D8 "),
      i("div", {
        class: W(["box", { open: s.value }])
      }, [
        i("div", we, [
          i("div", {
            class: "colorView",
            style: M({ "background-color": w(u).value })
          }, null, 4),
          i("div", {
            class: "defaultColor",
            onClick: Y,
            onMouseover: r[0] || (r[0] = (l) => d(e.defaultColor)),
            onMouseout: r[1] || (r[1] = (l) => d(""))
          }, "\u9ED8\u8BA4\u989C\u8272", 32)
        ]),
        i("div", Oe, [
          Ie,
          i("ul", Ee, [
            (k(), h(V, null, N(y, (l, c) => i("li", {
              key: c,
              style: M({ backgroundColor: l }),
              onMouseover: (f) => d(l),
              onMouseout: r[2] || (r[2] = (f) => d("")),
              onClick: (f) => E(l)
            }, null, 44, Pe)), 64))
          ]),
          i("ul", Me, [
            (k(!0), h(V, null, N(w(b), (l, c) => (k(), h("li", { key: c }, [
              i("ul", null, [
                (k(!0), h(V, null, N(l, (f, S) => (k(), h("li", {
                  key: S,
                  style: M({ backgroundColor: f }),
                  onMouseover: (T) => d(f),
                  onMouseout: r[3] || (r[3] = (T) => d("")),
                  onClick: (T) => E(f)
                }, null, 44, Ae))), 128))
              ])
            ]))), 128))
          ]),
          $e,
          i("ul", Te, [
            (k(), h(V, null, N(g, (l, c) => i("li", {
              key: c,
              style: M({ backgroundColor: l }),
              onMouseover: (f) => d(l),
              onMouseout: r[4] || (r[4] = (f) => d("")),
              onClick: (f) => E(l)
            }, null, 44, Be)), 64))
          ]),
          i("h3", { onClick: X }, "\u66F4\u591A\u989C\u8272..."),
          L(" \u7528\u4EE5\u6FC0\u6D3BHTML5\u989C\u8272\u9762\u677F "),
          ue(i("input", {
            type: "color",
            ref_key: "html5ColorEl",
            ref: x,
            "onUpdate:modelValue": r[5] || (r[5] = (l) => ae(m) ? m.value = l : null),
            onChange: r[6] || (r[6] = (l) => E(w(m)))
          }, null, 544), [
            [ie, w(m)]
          ])
        ])
      ], 2)
    ], 512));
  }
});
const Qe = (e, t) => {
  const o = e.__vccOpts || e;
  for (const [s, p] of t)
    o[s] = p;
  return o;
}, O = /* @__PURE__ */ Qe(Ne, [["__scopeId", "data-v-b46fc003"], ["__file", "/Users/<USER>/WORK/github/vue-color-picker/packages/color-picker/src/index.vue"]]);
O.install = function(e, t) {
  e.component(O.name, O);
};
console.log("name", O.name);
const Se = [
  O
], Fe = function(e) {
  Se.map((t) => e.component(t.name, t));
}, De = {
  install: Fe,
  colorPicker: O
};
export {
  De as default
};
