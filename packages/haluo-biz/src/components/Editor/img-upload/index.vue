<template>
  <BasicDialog :visible="props.visible" :showTitle="false">
    <div class="img-upload-content">
      <div class="title-box">
        <div
          v-for="(item, index) in titleList"
          :key="index"
          :class="['title', { active: item.id === activeTitle }]"
          @click="changeTab(item)"
        >
          {{ item.value }}
          <div v-if="item.id === activeTitle" class="line"></div>
        </div>
        <div
          v-if="[1, 3].includes(activeTitle) && props.imgList.length"
          class="prompt-restriction"
        >
          最多可选{{ props.imgNum }}张图片，已选{{ props.imgList.length }}张
        </div>
        <div
          v-if="activeTitle === 2 && props.imgList.length > 1"
          class="prompt-restriction"
        >
          {{ props.imgList.length }}张上传成功 拖动可调整顺序
        </div>
      </div>
      <div v-if="activeTitle === 1" class="text-picture">
        <div class="image-list content-image">
          <div
            v-for="(item, index) in contentImgList"
            :key="index"
            class="image-item"
            @click="selectImage(item, 1)"
          >
            <img :src="item.imgOrgUrl" alt="" class="image" />
            <div v-if="item.num" class="shade">
              <div class="num">{{ item.num }}</div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="activeTitle === 2"
        :class="['insert-img', !props.imgList.length ? 'no-img' : '']"
      >
        <div v-if="!props.imgList.length" class="upload-wrap">
          <div class="upload-wrap_box" @click="emitEvent">
            <el-icon class="icon pointer"><el-icon-picture /></el-icon>
            <span>本地上传</span>
          </div>
          <span class="remark"
            >支持jpg、jpeg、png等多种格式，单张图片最大支持20MB</span
          >
        </div>
        <div v-else class="img-list">
          <draggable
            v-model="imgListTemp"
            handle=".img-item"
            class="img-wrap"
            group="people"
            @start="drag = true"
            item-key="id"
            @end="drag = false"
          >
            <template #item="{ element }">
              <div :key="element.id" class="img-item">
                <img
                  :src="element.src"
                  style="object-fit: contain"
                  width="120"
                  height="120"
                />
                <el-icon-error class="icon pointer" @click="deleteImg(element)">
                </el-icon-error>
              </div>
            </template>
            <template #footer>
              <div class="upload-icon pointer" @click="emitEvent">
                <el-icon class="icon pointer"><el-icon-plus /></el-icon>
              </div>
            </template>
          </draggable>
        </div>
        <div class="upload-btn">
          <input
            type="file"
            name="file"
            multiple
            class="imageFile"
            accept="image/*"
            @change="selectImgs($event)"
          />
        </div>
      </div>
      <div v-if="activeTitle === 3" class="map-depot">
        <div class="screen">
          <div>
            <el-select
              v-model="selectData.brandId"
              placeholder="请选择品牌"
              size="large"
              class="select_1"
              filterable
              clearable
              @change="changeBrandId"
            >
              <el-option
                v-for="(item, index) in brandList"
                :key="index"
                :label="item.brandName"
                :value="item.brandId"
              />
            </el-select>
            <el-select
              v-model="selectData.goodId"
              placeholder="请选择车型"
              size="large"
              class="select_1"
              filterable
              clearable
              @change="changeGoodId"
            >
              <el-option
                v-for="(item, index) in goodList"
                :key="index"
                :label="item.goodName"
                :value="item.goodId"
              />
            </el-select>
            <el-select
              v-model="selectData.carId"
              placeholder="请选择款型"
              size="large"
              class="select_2"
              filterable
              clearable
            >
              <el-option
                v-for="(item, index) in carList"
                :key="index"
                :label="item.goodsCarName"
                :value="item.carId"
              />
            </el-select>
          </div>
          <el-button
            type="primary"
            size="large"
            style="width: 80px"
            @click="search"
            >搜索</el-button
          >
        </div>
        <div v-if="tabList && tabList.length">
          <div class="tab-box">
            <div
              v-for="(item, index) in tabList"
              :key="index"
              :class="['tab', { active: item.type === activeTab }]"
              @click="activeTab = item.type"
            >
              {{ item.name }}
              <div v-if="item.type === activeTab" class="line"></div>
            </div>
          </div>
          <div class="image-list depot-image">
            <div
              v-for="(item, index) in imageList"
              :key="index"
              class="image-item"
              @click="selectImage(item, 3)"
            >
              <img :src="item.imgOrgUrl" alt="" class="image" />
              <div v-if="item.num" class="shade">
                <div class="num">{{ item.num }}</div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="protocol-box">
          <div>
            <div>
              图库中的图片仅限下载摩托范以编辑发布文章使用,不得用于其他任何平台及用途
            </div>
            <div>
              请阅读<a
                :href="`https://${hostName}/about?type=imageLicenseAgreement`"
                target="_blank"
                rel="noopener noreferrer"
                class="link"
                >《摩托范图片许可使用协议》</a
              >,您的行为视为您已同意该协议。
            </div>
          </div>
        </div>
      </div>
    </div>
  </BasicDialog>
</template>
<script lang="ts" setup>
import { ElIcon, ElMessage, ElButton, ElSelect, ElOption } from 'element-plus'
import {
  Plus as ElIconPlus,
  Picture as ElIconPicture,
  CircleCloseFilled as ElIconError,
} from '@element-plus/icons-vue'
import BasicDialog from '../../basic-dialog/index.vue'
import draggable from 'vuedraggable'
import { v4 as uuidv4 } from 'uuid'
import {
  computed,
  withDefaults,
  defineProps,
  ref,
  provide,
  reactive,
  watch,
} from 'vue'

const emits = defineEmits(['update:imgList', 'update:visible', 'submit'])
const props = withDefaults(
  defineProps<{
    visible: boolean
    imgList: any
    chartGallery: boolean
    request: any
    imgNum: number
  }>(),
  {
    visible: false,
    imgList: [],
    chartGallery: false,
    request: {},
    imgNum: 20,
  }
)
const drag = ref(false)
const imgListTemp = computed({
  get() {
    return props.imgList
  },
  set(val) {
    emits('update:imgList', val)
  },
})
provide('vm', {
  submit() {
    emits('submit')
  },
  setStatus(val: boolean) {
    emits('update:visible', val)
  },
})
const selectImgs = (e: any) => {
  const imgFile = e.target.files
  const tempImgs = [...props.imgList]
  Array.from(imgFile).forEach((file: any) => {
    if (file.size > 20 * 1024 * 1024) {
      ElMessage.error('单张图片最大支持20M')
      return
    }
    const id = uuidv4()
    const url = URL.createObjectURL(file)
    tempImgs.push({ id, src: url, file: file })
  })
  e.target.value = ''
  emits('update:imgList', tempImgs)
}
const deleteImg = (img: any) => {
  const arr = props.imgList.filter((item: any) => item.id !== img.id)
  emits('update:imgList', arr)
}
const emitEvent = () => {
  var event = new MouseEvent('click')
  var ele = document.getElementsByClassName('imageFile')
  ele[0].dispatchEvent(event)
}

const activeTitle = ref(2)
const titleList = ref<any>([])

const hostName = location.hostname
const isEmotofine = hostName.includes('emotofine')
watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      const list = [
        {
          id: 2,
          value: '上传图片',
        },
      ]
      activeTitle.value = 2
      if (props.imgNum < 20) {
        list.unshift({
          id: 1,
          value: '正文图片',
        })
        activeTitle.value = 1
        getContentImgList()
      }
      if (props.chartGallery) {
        list.push({
          id: 3,
          value: isEmotofine ? '电摩范图库' : '摩托范图库',
        })
      }
      titleList.value = list
    }
  }
)
const changeTab = (item: any) => {
  if (activeTitle.value !== item.id) {
    activeTitle.value = item.id
    emits('update:imgList', [])
    if (item.id === 1) {
      if (!contentImgList.value.length) {
        getContentImgList()
      }
    }
    if (item.id === 3) {
      if (!brandList.value.length) {
        getBrandList()
      }
    }
  }
}

const contentImgListInit = ref<any>([])
const getContentImgList = () => {
  contentImgListInit.value = []
  const node = document.getElementById('editor-content')
  if (node) {
    const imgList = node.querySelectorAll('.halo-picture-area')
    if (imgList && imgList.length) {
      const list: any = []
      imgList.forEach((img: any) => {
        if (!list.includes(img.src)) {
          list.push(img.src)
        }
      })
      list.forEach((src: string, i: number) => {
        contentImgListInit.value.push({
          imgOrgUrl: src,
          imgId: `content_${i}`,
        })
      })
    }
  }
}
const contentImgList = computed(() => {
  const list: any = []
  contentImgListInit.value.forEach((v: any) => {
    const value = props.imgList.find((_: any) => _.id === v.imgId)
    list.push({
      imgOrgUrl: v.imgOrgUrl,
      imgId: v.imgId,
      num: (value && value.num) || 0,
    })
  })
  return list
})

const selectData = reactive({
  brandId: '',
  goodId: '',
  carId: '',
})
const {
  getCarTypeList,
  getAllBrandList,
  getGoodsImgsTypeData,
  getCarGoodsList,
} = props.request
const brandList = ref<any>([])
const getBrandList = () => {
  brandList.value = []
  getAllBrandList({
    brandEnergyType: 3,
    brandVersion: 1598596364,
  }).then((res: any) => {
    if (res.data.code === 0) {
      brandList.value = res.data.data || []
    }
  })
}
const goodList = ref<any>([])
const getGoodList = () => {
  goodList.value = []
  getCarTypeList(selectData.brandId, {
    platform: 11,
    provinceName: '',
    cityName: '',
    onSale: 1,
    page: 1,
    rows: 9999,
    brandEnergyType: 3,
    goodMinPrice: '',
    goodMaxPrice: '',
    goodType: '',
    goodMinVolume: '',
    goodMaxVolume: '',
    sortType: 0,
  }).then((res: any) => {
    if (res.data.code === 0) {
      goodList.value = res.data.data || []
    }
  })
}
const carList = ref<any>([])
const getCarList = () => {
  carList.value = []
  getCarGoodsList({
    goodId: selectData.goodId,
    color: '',
    isFromAskPrice: 0,
  }).then((res: any) => {
    if (res.data.code === 0) {
      const list = res.data.data || []
      list.forEach((v: any) => {
        if (v.carInfoList && v.carInfoList.length) {
          v.carInfoList.forEach((car: any) => {
            carList.value.push(car)
          })
        }
      })
    }
  })
}
const changeBrandId = (val: string) => {
  selectData.goodId = ''
  selectData.carId = ''
  if(!val) {
    goodList.value = []
    carList.value = []
    return 
  }
  getGoodList()
}
const changeGoodId = (val: string) => {
  selectData.carId = ''
  if(!val) {
    carList.value = []
    return 
  }
  getCarList()
}

const activeTab = ref('1')
const tabList = ref<any>([])
const search = () => {
  if (!selectData.brandId) {
    return ElMessage.error('请选择品牌')
  }
  if (!selectData.goodId) {
    return ElMessage.error('请选择车型')
  }
  if (!selectData.carId) {
    return ElMessage.error('请选择款型')
  }
  tabList.value = []
  emits('update:imgList', [])
  getGoodsImgsTypeData({
    goodId: selectData.goodId,
    carId: selectData.carId,
  }).then((res: any) => {
    if (res.data.code === 0) {
      tabList.value = res.data.data || []
      if (tabList.value.length) {
        activeTab.value = tabList.value[0].type || '1'
      }
    }
  })
}
const imageList = computed(() => {
  const list: any = []
  const item = tabList.value.find((v: any) => v.type === activeTab.value)
  if (item && item.imgList && item.imgList.length) {
    item.imgList.forEach((v: any) => {
      const value = props.imgList.find((_: any) => _.id === v.imgId)
      list.push({
        imgOrgUrl: v.imgOrgUrl,
        imgId: v.imgId,
        num: (value && value.num) || 0,
      })
    })
  }
  return list
})
const selectImage = (item: any, type: number) => {
  const list = [...props.imgList]
  const index = list.findIndex((v: any) => v.id === item.imgId)
  if (index >= 0) {
    list.splice(index, 1)
  } else {
    if (list.length >= props.imgNum) return
    list.push({
      id: item.imgId,
      src: item.imgOrgUrl,
      type,
    })
  }
  list.forEach((v: any, i: number) => {
    v.num = i + 1
  })
  emits('update:imgList', list)
}
</script>
<style lang="less">
.img-upload-content {
  .title-box {
    padding: 0 10px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .title {
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      margin-right: 30px;
      cursor: pointer;
    }
    .active {
      font-weight: bold;
      position: relative;
      .line {
        position: absolute;
        left: 0;
        right: 0;
        bottom: -8px;
        height: 2px;
        background-color: #ff3c08;
      }
    }
    .prompt-restriction {
      font-size: 14px;
      color: #ff5a25;
      line-height: 20px;
    }
  }
  .text-picture {
    padding: 0 10px;
  }
  .map-depot {
    padding: 0 10px;
    .screen {
      padding-top: 15px;
      border-top: 1px solid #e8e8e8;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .select_1 {
        width: 166px;
        margin-right: 20px;
      }
      .select_2 {
        width: 256px;
      }
      .el-button--primary {
        background: #ff5a25;
        border-color: #ff5a25;
      }
    }
    .tab-box {
      padding: 20px 0;
      display: flex;
      align-items: center;
      .tab {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-right: 30px;
        cursor: pointer;
      }
      .active {
        font-weight: bold;
        position: relative;
        .line {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          height: 2px;
          background-color: #ff3c08;
        }
      }
    }
    .protocol-box {
      height: 344px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 14px;
      line-height: 24px;
      color: #999999;
      .link {
        font-size: 14px;
        line-height: 24px;
        color: #3484de;
      }
    }
  }
  .image-list {
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    .image-item {
      position: relative;
      background-color: #d8d8d8;
      cursor: pointer;
      .image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      .shade {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.4);
        .num {
          display: inline-block;
          width: 28px;
          height: 28px;
          margin-left: 10px;
          margin-top: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 16px;
          border-radius: 50%;
          background-color: #ff5a25;
        }
      }
    }
    &.content-image {
      height: 400px;
      padding-top: 20px;
      .image-item {
        width: 120px;
        height: 120px;
        margin-right: 12px;
        margin-bottom: 20px;
        &:nth-child(6n) {
          margin-right: 0;
        }
      }
    }
    &.depot-image {
      height: 284px;
      .image-item {
        width: 180px;
        height: 120px;
        margin-right: 20px;
        margin-bottom: 15px;
        &:nth-child(4n) {
          margin-right: 0;
        }
      }
    }
  }
}
.insert-img {
  overflow-y: auto;
  padding: 0 10px;
  height: 400px;
  &.no-img {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .upload-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .upload-wrap_box {
      font-size: 14px;
      cursor: pointer;
      color: #999999;
      width: 310px;
      height: 210px;
      border-radius: 10px;
      border: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      span {
        margin-top: 10px;
      }
      i {
        font-size: 50px;
        color: #9e9e9e;
      }
    }
    .remark {
      font-size: 14px;
      color: #999999;
      margin-top: 20px;
    }
  }
  .upload-btn {
    display: none;
  }
  .img-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding-top: 20px;
    .img-item {
      position: relative;
      margin-right: 12px;
      margin-bottom: 20px;
      background-color: #d8d8d8;
      &:nth-child(6n) {
        margin-right: 0;
      }
      .icon {
        width: 18px;
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
      }
    }
  }
  .img-wrap {
    display: flex;
    flex-wrap: wrap;
  }
  .upload-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120px;
    height: 120px;
    background: #fafafa;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
  }
}
</style>
