@import "../../assets/css/util";
@editor-box-color: #f4f4f4;
@base-bg-color: #f5f5f5; // 主背景色
@base-bg-color1: #26262a; // 主背景色 滑@seven-color色值(1.3.6)
@brand-color: #FF3C08; // 品牌色（用于多出突出主体的位置) 按钮色值、底部标签栏当前模块色值等

@one-size: 14px;
@one-color: #333; //toast提示图标(1.3.6)
@three-color: #999; // tab 切换未选择颜色，圈子评论文字颜色(1.3.6)

.editor {
    position: relative;
    min-width: 7rem;
    margin-top: .2rem;

    mdd-topic {
        pointer-events: none;
        user-select: none;
        color: #5288F6;
    }

    // 话题弹框样式
    .topic-popover-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 999;
        background: transparent;
    }

    .topic-popover {
        position: absolute;
        z-index: 1000;
        background: #fff;
        border-radius: 4px;
        box-sizing: border-box;
        box-shadow: 0 4px 17px rgba(0, 0, 0, .12);
        width: 430px;
        height: 286px;
        overflow-y: auto;

        .topic-popover-content {
            .topic-tabs {
                display: flex;
                border-bottom: 1px solid #f0f0f0;
                background: #fafafa;

                .topic-tab {
                    flex: 1;
                    padding: 12px 16px;
                    text-align: center;
                    font-size: 14px;
                    color: #666;
                    cursor: pointer;
                    border-bottom: 2px solid transparent;
                    transition: all 0.2s;

                    &:hover {
                        color: @brand-color;
                        background: #f5f5f5;
                    }

                    &.active {
                        color: @brand-color;
                        border-bottom-color: @brand-color;
                        background: #fff;
                    }
                }
            }

            .topic-search-header {
                padding: 12px 16px;
                border-bottom: 1px solid #f0f0f0;
                background: #fafafa;

                .topic-search-title {
                    font-size: 14px;
                    color: #333;
                    font-weight: 500;
                }
            }

            .topic-list-container {
                max-height: 240px;
                overflow-y: auto;

                .topic-list {
                    .topic-item {
                        padding: 12px 16px;
                        font-size: 14px;
                        color: #333;
                        cursor: pointer;
                        border-bottom: 1px solid #f8f8f8;
                        transition: background-color 0.2s;

                        &:hover {
                            background: #f5f5f5;
                            color: @brand-color;
                        }

                        &:last-child {
                            border-bottom: none;
                        }
                    }

                    .topic-loading,
                    .topic-empty {
                        padding: 20px 16px;
                        text-align: center;
                        font-size: 14px;
                        color: #999;
                    }
                }
            }
        }
    }

    ol,
    ul {
        margin: 0 1em;
        padding: 0 1em;
        margin-top: 10px;
        list-style-type: decimal;

        li {
            list-style-position: inside;
        }
    }

    ol>li {
        list-style-type: decimal;
        margin-bottom: 10px;
        font-size: 17px;
    }

    ul>li {
        list-style-type: disc;
        margin-bottom: 10px;
        font-size: 17px;
    }

    .fixed-top {
        position: fixed !important;
        top: .6rem;
        z-index: 1000;
    }

    .tool-box {
        position: absolute;
        width: 9.72rem;
        height: .4rem;
        background-color: @editor-box-color;

        .icon-section {
            display: inline-block;
            position: relative;

            .tip {
                display: none;
                position: absolute;
                width: .7rem;
                background-color: @base-bg-color1;
                color: @base-bg-color;
                border-radius: .04rem;
                padding: .04rem;
            }
        }

        .icon-section:hover {
            .tip {
                display: block;
            }
        }

        .icon {
            cursor: pointer;
            display: inline-block;
            .wh(.4rem, .4rem);
        }

    }

    .placeholder {
        pointer-events: none;
        position: absolute;
        font-size: 18px;
        width: 100%;
        color: #999999;
        top: 15px;
    }

    #editor-content {
        position: relative;
        pointer-events: auto;
        resize: vertical;
        padding: 14px 0;
        outline: none;
        min-height: 6.4rem;
        border-bottom: 0.01rem solid @editor-box-color;
        border-top: 0;

        u,
        i {
            font-weight: inherit;
        }

        .halo-paragraph {
            .sc(17px, @one-color);
            line-height: 1.5;
        }

        .halo-paragraph-title {
            line-height: 25px;
            font-weight: 600;
            .sc(20px, @one-color);
            border-left: 4px solid @brand-color;
            padding: 10px;
            margin: 15px 0;
        }

        .halo-img-content {

            position: relative;
            // display: flex;
            // width: 100%;
            // justify-content: center;
            text-align: center;
            // left: 250px;
            outline: none;

            .img-loading,
            .img-fail {
                user-select: none;
                position: absolute;
                display: flex;
                align-items: center;
                justify-content: center;
                top: 0;
                width: 440px;
                height: 100%;
                left: 50%;
                transform: translateX(-50%);
                line-height: 25px;
                font-size: 18px;
                z-index: 1;
                opacity: .9;
                color: #fff;
                user-select: none;


                .img-loading-icon {
                    width: 74px;
                    margin-left: 40px;
                }

                .img-loading-tip {
                    position: relative;
                    left: -60px;
                    top: 2px;
                }
            }

            .img-again {
                user-select: none;
                position: absolute;
                left: 175px;
                bottom: 60px;
                font-size: 14px;
                font-weight: 400;
                color: #fff;
                z-index: 1;
                left: 50%;
                transform: translateX(-50%);
                width: 90px;
                line-height: 32px;
                height: 32px;
                border-radius: 4px;
                background: rgba(0, 0, 0, 0.5);
                user-select: none;
                cursor: pointer;
            }

            .img-replace {
                user-select: none;
                position: absolute;
                bottom: 80px;
                color: white;
                background: rgba(0, 0, 0, 0.5);
                display: inline-block;
                width: 80px;
                height: 32px;
                line-height: 32px;
                border-radius: 4px;
                left: 50%;
                // left: 220px;
                transform: translateX(-50%);
            }

            .desc-input-wrap {
                // width: 440px;
                margin-top: 20px;

                textarea {
                    user-select: text;
                    outline: none;
                    text-align: center;
                    outline: none;
                    border: none;
                    resize: none;
                    color: #999999;

                    &:disabled {
                        background: white;
                    }
                }
            }

            .img-delete {
                user-select: none;
                font-size: 18px;
                width: 18px;
                height: 18px;
                border-radius: 50%;
                line-height: 18px;
                text-align: center;
                // left: 440px;
                position: absolute;
                top: 0;
                transform: translate(-40%, -50%);
            }

            .halo-picture-area {
                display: inline-block;
                width: 440px;
                min-height: 150px;
                margin: 5px 0;
            }

            .halo-picture-area:focus {
                outline: 2px solid #1a74ff;
            }


            .no-upload,
            .upload-fail {
                filter: brightness(0.5);
            }


            // &::before {
            //     content: '上传失败，请下载图片至本地后重新上传';
            //     position: absolute;
            //     width: 150px;
            //     line-height: 15px;
            //     top: 10px;
            //     left: 10px;
            //     text-align: center;
            //     color: #fff;
            //     z-index: 1;
            // }

            // &::after {
            //     content: '重新上传';
            //     position: relative;
            //     display: block;
            //     width: 100px;
            //     height: 20px;
            //     top: -30px;
            //     margin-bottom: -20px;
            //     border-radius: 5px;
            //     text-align: center;
            //     color: #fff;
            //     background: rgba(0, 0, 0, .3);
            //     z-index: 1;
            // }
        }

        .halo-modify-content {
            position: relative;
            // display: flex;
            // width: 100%;
            // justify-content: center;
            text-align: center;
            // left: 250px;
            outline: none;

            .img-loading,
            .img-fail {
                user-select: none;
                position: absolute;
                display: flex;
                align-items: center;
                justify-content: center;
                top: 0;
                width: 440px;
                height: 100%;
                left: 50%;
                transform: translateX(-50%);
                line-height: 25px;
                font-size: 18px;
                z-index: 1;
                opacity: .9;
                color: #fff;
                user-select: none;


                .img-loading-icon {
                    width: 74px;
                    margin-left: 40px;
                }

                .img-loading-tip {
                    position: relative;
                    left: -60px;
                    top: 2px;
                }
            }

            .img-again {
                user-select: none;
                position: absolute;
                left: 175px;
                bottom: 60px;
                font-size: 14px;
                font-weight: 400;
                color: #fff;
                z-index: 1;
                left: 50%;
                transform: translateX(-50%);
                width: 90px;
                line-height: 32px;
                height: 32px;
                border-radius: 4px;
                background: rgba(0, 0, 0, 0.5);
                user-select: none;
                cursor: pointer;
            }

            .img-replace {
                display: none;
            }

            .desc-input-wrap {
                display: none;
            }

            .img-delete {
                display: none;
            }

            .halo-modify-area {
                display: inline-block;
                width: 440px;
                min-height: 150px;
                margin: 5px 0;
            }

            .halo-modify-area:focus {
                outline: 2px solid #1a74ff;
            }


            .no-upload,
            .upload-fail {
                filter: brightness(0.5);
            }
        }

        .halo-select {
            outline: 2px solid #1a74ff;
        }

        .halo-video-content {
            margin: 5px;
            padding-right: 5px;
            position: relative;
            outline: none;

            .video-delete {
                width: 18px;
                position: absolute;
                font-size: 18px;
                left: calc(50% + 250px);
                top: 0;
                transform: translate(-50%, -50%);
            }
        }

        .video-cover-replace {
            cursor: pointer;
            position: absolute;
            top: 10px;
            left: 24%;
            text-align: center;
            font-size: 14px;
            width: 88px;
            line-height: 32px;
            border-radius: 4px;
            background-color: rgba(0, 0, 0, .5);
            color: #fff;
        }

        .halo-video-area {
            max-height: 200px;
            width: 500px;
            display: block;
            pointer-events: none;
            margin: 0 auto;
            object-fit: cover;
        }

        .video-title {
            max-height: 110px;
        }



        .halo-link {
            display: flex;
            position: relative;
            margin: 10px 0;
            width: 100%;
            .sc(@one-size, #2eaee5);
            line-height: 17px;

            .img-delete {
                user-select: none;
                position: absolute;
                right: 0;
                top: 0;
                width: 25px;
                height: 25px;
            }
        }

        .link-img {
            position: relative;
            .wh(20px, 20px);
        }

        .halo-link-mes {
            margin: 0;
            .sc(@one-size, #2eaee5);
            border: 0;
            max-height: 100px;
            line-height: 17px;
            resize: none;
            width: 100%;
            outline: none;
            word-wrap: break-word;
            display: inline-block;
        }

        .video-progress {
            width: 440px;
            height: 220px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin: 0 auto;
            padding: 0 40px;
            background: #F8F9FB;

            .label {
                width: 100%;
                margin-bottom: 20px;
                font-size: 18px;
                font-weight: bold;
            }

            .box {
                width: 360px;
                height: 6px;
                background: #E9E9E9;
                border-radius: 3px;
                overflow: hidden;
            }

            .inner {
                background: #FF5A25;
                height: 6px;
            }
        }
    }

    .preview {
        position: fixed;
        top: 0;
        left: 0;
        .wh(100vw, 100vh);
        z-index: 10;
        overflow-y: scroll;

        .preview-bg {
            position: fixed;
            top: 0;
            left: 0;
            .wh(100vw, 100vh);
            background-color: rgba(0, 0, 0, .3);
        }


    }

    .overline {
        position: absolute;
        z-index: 50;
        pointer-events: none;
        background-color: rgb(26, 116, 255);
        width: 100%;
        height: 1px;
    }
}

.video-progress {
    width: 440px;
    height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 40px;
    background: #F8F9FB;

    .label {
        width: 100%;
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
    }

    .box {
        width: 360px;
        height: 6px;
        background: #E9E9E9;
        border-radius: 3px;
        overflow: hidden;
    }

    .inner {
        background: #FF5A25;
        height: 6px;
    }
}