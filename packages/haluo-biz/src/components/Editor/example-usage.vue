<template>
  <div class="editor-example">
    <h2>编辑器话题功能示例</h2>
    
    <div class="example-container">
      <Editor 
        ref="editor"
        :request="request"
        @backData="handleBackData"
      />
    </div>
    
    <div class="example-info">
      <h3>使用说明：</h3>
      <ul>
        <li>输入 <code>#</code> 号会弹出热门话题弹框</li>
        <li>输入 <code>#摩托</code> 等关键词会弹出搜索话题弹框</li>
        <li>点击话题可插入到编辑器中</li>
        <li>话题会自动保存到最近使用</li>
      </ul>
      
      <h3>当前编辑器状态：</h3>
      <pre>{{ editorStatus }}</pre>
    </div>
  </div>
</template>

<script>
import Editor from './Editor.vue'

export default {
  name: 'EditorExample',
  components: {
    Editor
  },
  data() {
    return {
      editorStatus: {
        contentLen: 0,
        imgCount: 0,
        titleCount: 0
      },
      request: {
        getTopic: this.getTopicApi,
        // 其他可能需要的API方法
        copyToEssayImg: this.copyToEssayImg
      }
    }
  },
  methods: {
    // 模拟话题API
    async getTopicApi(params) {
      console.log('调用话题API:', params)
      
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟热门话题数据
      if (params.hoopId !== undefined) {
        return {
          data: {
            code: 0,
            data: [
              {
                id: 1,
                exactlyMatchTitle: '摩托车',
                autherid: 123,
                background: '',
                creatTime: Date.now(),
                fans: 100,
                logo: '',
                recommendSort: 1,
                shortType: 'short_topic',
                sort: 1,
                status: 1,
                title: '摩托车',
                type: 0,
                view: 1000
              },
              {
                id: 2,
                exactlyMatchTitle: '骑行',
                autherid: 124,
                background: '',
                creatTime: Date.now(),
                fans: 80,
                logo: '',
                recommendSort: 2,
                shortType: 'short_topic',
                sort: 2,
                status: 1,
                title: '骑行',
                type: 0,
                view: 800
              },
              {
                id: 3,
                exactlyMatchTitle: '机车文化',
                autherid: 125,
                background: '',
                creatTime: Date.now(),
                fans: 60,
                logo: '',
                recommendSort: 3,
                shortType: 'short_topic',
                sort: 3,
                status: 1,
                title: '机车文化',
                type: 0,
                view: 600
              }
            ]
          }
        }
      }
      
      // 模拟搜索话题数据
      if (params.title) {
        const searchResults = [
          {
            id: 10,
            exactlyMatchTitle: `${params.title}相关话题1`,
            autherid: 126,
            background: '',
            creatTime: Date.now(),
            fans: 50,
            logo: '',
            recommendSort: 0,
            shortType: 'short_topic',
            sort: 0,
            status: 1,
            title: `${params.title}相关话题1`,
            type: 0,
            view: 400
          },
          {
            id: 11,
            exactlyMatchTitle: `${params.title}相关话题2`,
            autherid: 127,
            background: '',
            creatTime: Date.now(),
            fans: 30,
            logo: '',
            recommendSort: 0,
            shortType: 'short_topic',
            sort: 0,
            status: 1,
            title: `${params.title}相关话题2`,
            type: 0,
            view: 200
          }
        ]
        
        return {
          data: {
            code: 0,
            data: searchResults
          }
        }
      }
      
      return {
        data: {
          code: 0,
          data: []
        }
      }
    },
    
    // 模拟图片复制API
    async copyToEssayImg(params) {
      console.log('复制图片API:', params)
      return {
        data: {
          code: 0,
          data: params.imgUrls // 直接返回原URL
        }
      }
    },
    
    handleBackData(flag, contentLen, imgCount) {
      this.editorStatus = {
        contentLen,
        imgCount,
        titleCount: 0 // 可以从编辑器获取
      }
      console.log('编辑器数据变化:', { flag, contentLen, imgCount })
    },
    
    // 获取编辑器HTML内容
    getEditorHtml() {
      if (this.$refs.editor) {
        const html = this.$refs.editor.getHtml()
        console.log('编辑器HTML:', html)
        return html
      }
      return ''
    },
    
    // 设置编辑器内容
    setEditorContent(html) {
      if (this.$refs.editor) {
        this.$refs.editor.initData(html)
      }
    }
  },
  
  mounted() {
    // 示例：设置初始内容
    setTimeout(() => {
      const sampleContent = `
        <p class="halo-paragraph">欢迎使用话题功能！试试输入 # 号</p>
        <p class="halo-paragraph">这里有一个示例话题：<mdd-topic data-topic='{"topicId":"sample","topicType":0,"startIndex":"8","endIndex":"12"}'>#示例话题</mdd-topic> 很有趣</p>
      `
      this.setEditorContent(sampleContent)
    }, 1000)
  }
}
</script>

<style scoped>
.editor-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.example-container {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  background: #fff;
}

.example-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.example-info h3 {
  margin-top: 0;
  color: #333;
}

.example-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.example-info li {
  margin: 5px 0;
}

.example-info code {
  background: #e9ecef;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.example-info pre {
  background: #fff;
  border: 1px solid #ddd;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
