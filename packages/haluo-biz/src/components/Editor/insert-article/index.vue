<template>
  <BasicDialog :visible="visible" @submit="confirm">
    <template #title>
      <span >插入文章/视频<strong style="margin-left: 20px; color: #FF5A25">平台仅支持插入{{`${ isVideo ? '2年' : '18个月'}`}}内的推荐内容</strong></span>
    </template>
    <div class="insert-article">
      <div>
        <el-radio v-model="data.params.radio" :label="Radio_Type.MY_ARITICLE">从我的文章中选择</el-radio>
        <el-radio v-model="data.params.radio" :label="Radio_Type.MY_VIDEO">从我的视频中选择</el-radio>
        <el-radio v-model="data.params.radio" :label="Radio_Type.ALL_ARITICLE">从所有文章中选择</el-radio>
      </div>
      <div class="insert-article_input">
        <el-input
          v-model="data.params.title"
          :placeholder="isVideo ? '输入视频描述查找平台发布过的视频' : '输入标题名查找平台发过的文章'"
          @keyup.enter.native="getArticleList(true)"
          @input="getArticleList(true)">
          <i slot="suffix" class="pointer el-input__icon el-icon-search" @click="getArticleList(true)"></i>
        </el-input>

      </div>
      <div v-if="data.data.length === 0 && !data.busy" class="nodata"> {{`没有符合条件的${isVideo ? '视频' : '文章'}哦，你可以试试搜索其他${isVideo ? '视频' : '文章'}～`}}</div>
      <div v-infinite-scroll="getArticleList" v-else :infinite-scroll-immediate="false" class="insert-article_list" :infinite-scroll-disabled="disable">
        <div v-for="(item, index) in data.data" :key="index" class="insert-article_list-item">
          <el-radio v-model="data.radio" :label="item.id" @change="select(item)">
            <template v-if="isVideo">
                <div class="video-item" >
                  <div class="video-pic">
                    <img class="video-pic" :src="item?.mediaInfo[0]?.img" />
                    <img class="play" :src="getImageUrl('play.png')"/>
                  </div>
                  <div class="video-text">
                    <span class="dotdotdot2">{{ item.content || '[无标题]'}}</span>
                    <span>{{ (date as any).format(item.createTime*1000, 'YYYY-MM-DD hh-mm') }}</span>
                  </div>
              </div>
            </template>
            <template v-else>
              <div style="display: inline-flex;flex-direction: column;">
              <span>{{ item.title || '[无标题]' }}</span>
              <span>{{ (date as any).format(item.createTime*1000, 'YYYY-MM-DD') }}</span>
            </div>
            </template>
          </el-radio>
          <!-- <div class="insert-article_list-item_title">
            <span>{{ item.title || '[无标题]' }}</span>
            <span>{{ $date.format(item.createTime*1000, 'YYYY-MM-DD') }}</span>
          </div> -->
          <!-- <span class="view pointer" @click="view(item)">查看文章{{ item.name }}</span> -->
        </div>
      </div>

    </div>
  </BasicDialog>
</template>
<script lang="ts" setup>
import BasicDialog from '../../basic-dialog/index.vue'
import { ElInput, ElRadio, ElMessage, ElInfiniteScroll } from 'element-plus'
// import { getArticleList } from '@/assets/js/api'
import date from '@haluo/util/dist/modules/date'
import { reactive, getCurrentInstance, provide, toRefs, watch, onMounted } from 'vue';
import { computed } from 'vue';
import tools from '@haluo/util/dist/modules/tools';
const { debounce } = tools as any

const vInfiniteScroll = ElInfiniteScroll
const Radio_Type = {
   MY_ARITICLE: '1',
   ALL_ARITICLE: '2',
   MY_VIDEO: '4',
}
const getImageUrl=(name: string) => {
  return new URL(`../../../assets/img/${name}`, import.meta.url).href
}
provide("vm", {
  submit() {
    emits("submit");
  },
  setStatus(val:boolean) {
    emits("update:visible", val);
  },
});
const props = withDefaults(
  defineProps<{
    visible: boolean,
    getList: (...args:any[]) => any
  }>(),
  {
    visible: false,
  }
)
const { visible, getList } = toRefs(props)
const emits = defineEmits(['change', 'submit', 'update:visible'])
const data = reactive<{
  [key:string]:any
}>({
  params: {
    autherid: '',
    radio: '1',
    title: '',
  },
  article: '',
  finished: false,
  page: 0,
  radio: '',
  data: [],
  busy: true
})
const disable = computed(() => {
  return data.finished || data.busy
})
const isVideo = computed(() => {
  return data.params.radio === Radio_Type.MY_VIDEO
})
watch(() => data.params.radio, (_) => {
  getArticleList(true)
})
onMounted(() => {
  getArticleList(true)
})
const select = (item:any) => {
  data.article = item
}
const confirm = () => {
  if (!data.article) {
    ElMessage.error(`请选择${isVideo ? '视频' : '文章'}`)
    return
  }
  data.article.isVideo = isVideo
  emits('change', data.article)
}
const view = (item:any) => {
  window.open(`https://www.jddmoto.com/article/0/${item.id}.html`)
}
 const getArticleList = debounce(async(reset:boolean) => {
  if (reset) {
    data.finished = false
    data.page = 0
    data.data = []
  }
  if (data.finished) return
  data.busy = true
  const userInfo = JSON.parse(localStorage.getItem('user')!) ||  JSON.parse(localStorage.getItem('userInfo')!)
  const userId = userInfo.uid || userInfo.userid
  const res = await getList.value({
    uid: data.params.radio !== Radio_Type.ALL_ARITICLE ? userId : '',
    autherid: data.params.radio !== Radio_Type.ALL_ARITICLE ? userId : '',
    limit: 20,
    allType: data.params.radio, // 只有4 是查视频 其他是文章
    title: data.params.title,
    page: ++data.page
  })
  if (res.data.code === 0) {
    const total = res.data.data.total
    data.data = data.data.concat(res.data.data.listData)
    if (data.data.length >= total) {
      data.finished = true
    }
  }
  data.busy = false
},1000)
</script>
<style lang="less">
.insert-article{
  margin-top: 30px;
  .nodata{
    text-align: center;
    color: #999999;
    font-size: 14px;
    margin-top: 40px;
  }
  .insert-article_input{
    margin-top: 20px;
  }
  .insert-article_list{
    height: 304px;
    overflow: auto;
    border: 1px solid #F6F6F8;
    margin-top: 4px;
    &-item{
      display: flex;
      align-items: center;
      min-height: 56px;
      padding-left: 20px;
      padding-right: 30px;
      .el-radio {
        height: auto;
      }
      .el-radio__label{
        display: inline-block;
        vertical-align: middle;
      }
      &:not(:last-child){
        border-bottom: 1px solid #F6F6F8;
        span:nth-child(1){
          line-height: 20px;
          font-size: 14px;
        }
        span:nth-child(2){
          line-height: 17px;
          font-size: 12px;
        }
      }
      &_title{
        display: flex;
        flex-direction: column;
        flex: 1;

      }
    }
  }
  .view{
    color: #5388F5;
  }
  .video-item {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    padding: 0.12rem 0;

    .video-pic {
      position: relative;
      width: 1.5rem;
      height: 1rem;
      border-radius: 0.05rem;
      .play {
          position: absolute;
          top: 0.4rem;
          left: 0.7rem;
          width: 0.16rem;
          height: 0.19rem;
        }
    }

    .video-text {
      display: flex;
      flex: 1;
      justify-content: space-between;
      padding: 0 0.1rem;
      height: 0.7rem;
      flex-direction: column;
      color: #333;
    }
  }
}
</style>
