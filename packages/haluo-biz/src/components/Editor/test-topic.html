<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>话题弹框测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-editor {
            border: 1px solid #ddd;
            min-height: 200px;
            padding: 15px;
            border-radius: 4px;
            outline: none;
            font-size: 16px;
            line-height: 1.5;
        }
        
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
        }
        
        mdd-topic {
            color: #5288F6;
            pointer-events: none;
            user-select: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="instructions">
            <h3>话题弹框功能测试说明</h3>
            <ul>
                <li>在编辑器中输入 <strong>#</strong> 号，应该弹出热门话题弹框</li>
                <li>在热门话题弹框中可以切换到"最近使用"标签</li>
                <li>输入 <strong>#摩托</strong> 等关键词，应该弹出搜索话题弹框</li>
                <li>点击话题项目会插入到编辑器中</li>
                <li>点击空白处关闭弹框</li>
                <li>不会在 <strong># </strong> (有空格) 或 <strong>#@</strong> 情况下触发弹框</li>
            </ul>
        </div>
        
        <div 
            class="test-editor" 
            contenteditable="true" 
            placeholder="在这里输入内容，尝试输入 # 号..."
        >
            <p>这是一个测试编辑器。试试输入 # 号看看话题弹框功能！</p>
            <p>已有话题示例：<mdd-topic data-topic='{"topicId":"12345","topicType":0,"startIndex":"12","endIndex":"16"}'>#摩托车</mdd-topic> 很酷</p>
        </div>
    </div>

    <script>
        // 简单的测试脚本
        document.addEventListener('DOMContentLoaded', function() {
            const editor = document.querySelector('.test-editor');
            
            editor.addEventListener('keydown', function(event) {
                console.log('按键:', event.key);
                
                if (event.key === '#') {
                    console.log('检测到 # 号输入，应该显示热门话题弹框');
                    // 这里在实际组件中会调用 showTopicPopover('hot', position)
                }
                
                // 简单的话题删除逻辑测试
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const startContainer = range.startContainer;
                    let isInMddTopic = false;

                    if (startContainer.nodeType === Node.ELEMENT_NODE && 
                        startContainer.tagName === 'MDD-TOPIC') {
                        isInMddTopic = true;
                    } else if (startContainer.nodeType === Node.TEXT_NODE) {
                        const parent = startContainer.parentNode;
                        if (parent.tagName === 'MDD-TOPIC') {
                            isInMddTopic = true;
                        }
                    }

                    if (isInMddTopic) {
                        if (event.key === 'Enter') {
                            event.preventDefault();
                            console.log('在话题中禁止回车');
                            return;
                        }

                        if (event.key === 'Backspace' || event.key === 'Delete') {
                            if (startContainer.nodeType === Node.ELEMENT_NODE && 
                                startContainer.tagName === 'MDD-TOPIC') {
                                startContainer.remove();
                                event.preventDefault();
                                console.log('删除话题元素');
                                return;
                            } else if (startContainer.nodeType === Node.TEXT_NODE) {
                                const parent = startContainer.parentNode;
                                if (parent.tagName === 'MDD-TOPIC') {
                                    parent.remove();
                                    event.preventDefault();
                                    console.log('删除话题元素');
                                    return;
                                }
                            }
                        }

                        const isPrintableKey = event.key.length === 1;
                        if (isPrintableKey) {
                            event.preventDefault();
                            console.log('在话题中禁止输入其他内容');
                            return;
                        }
                    }
                }
            });
            
            // 模拟插入话题的功能
            window.insertTestTopic = function() {
                const selection = window.getSelection();
                if (selection.rangeCount === 0) return;
                
                const range = selection.getRangeAt(0);
                const topicElement = document.createElement('mdd-topic');
                topicElement.setAttribute('data-topic', JSON.stringify({
                    topicId: "test123",
                    topicType: 0,
                    startIndex: 0,
                    endIndex: 0
                }));
                topicElement.textContent = '#测试话题';
                
                range.deleteContents();
                range.insertNode(topicElement);
                
                const spaceNode = document.createTextNode(' ');
                range.setStartAfter(topicElement);
                range.insertNode(spaceNode);
                
                range.setStartAfter(spaceNode);
                range.collapse(true);
                selection.removeAllRanges();
                selection.addRange(range);
                
                console.log('插入测试话题成功');
            };
            
            console.log('话题弹框测试页面已加载');
            console.log('可以调用 insertTestTopic() 来测试插入话题功能');
        });
    </script>
</body>
</html>
