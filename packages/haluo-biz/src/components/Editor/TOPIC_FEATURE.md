# 话题弹框功能说明

## 功能概述

在Editor组件中新增了话题弹框功能，用户可以通过输入`#`号来触发话题选择弹框，支持热门话题和搜索话题两种模式。

## 功能特性

### 1. 热门话题弹框
- **触发条件**: 用户输入`#`号
- **功能**: 显示热门话题列表和最近使用的话题
- **支持**: 
  - 热门话题/最近使用标签切换
  - 滚动加载更多热门话题
  - 点击选择话题

### 2. 搜索话题弹框
- **触发条件**: 在`#`号后输入其他字符（且`#`号右侧没有空格）
- **功能**: 根据输入内容搜索相关话题
- **支持**: 
  - 实时搜索
  - 滚动加载更多搜索结果

### 3. 话题插入
- **格式**: `<mdd-topic data-topic='{"topicId":"12345","topicType":0,"startIndex":"12","endIndex":"16"}'>#话题名</mdd-topic> `
- **特性**: 
  - 自动添加空格
  - 自动更新位置信息
  - 保存到最近使用

### 4. 话题编辑限制
- **删除**: 整体删除，不能部分删除
- **编辑**: 禁止在话题内编辑内容
- **回车**: 禁止在话题内回车

## 不触发弹框的情况

1. `# ` (井号后有空格)
2. `#@` (井号后紧跟@符号)
3. 在input或textarea元素中输入

## API接口

### 热门话题接口
```javascript
this.request.getTopic({
  action: '201023',
  page: 1,
  limit: 50,
  hoopId: 0,
  type: 0,
  orderBy: 'view'
})
```

### 搜索话题接口
```javascript
this.request.getTopic({
  action: '201023',
  page: 1,
  limit: 50,
  title: '搜索关键词',
  highlightTitle: 'title'
})
```

## 数据结构

### 话题数据格式
```javascript
{
  "autherid": 2011059,
  "background": "",
  "creatTime": 1567808381,
  "exactlyMatchTitle": "推荐话题6", // 显示的话题名称
  "fans": 0,
  "id": 11605, // 话题ID
  "logo": "",
  "recommendSort": 0,
  "shortType": "short_topic",
  "sort": 0,
  "status": 1,
  "title": "是",
  "type": 0,
  "view": 0
}
```

### 本地存储
- **字段名**: `localTopic`
- **格式**: JSON数组，存储最近使用的话题
- **限制**: 最多保存20个话题

## 样式说明

话题弹框使用了以下CSS类：
- `.topic-popover`: 弹框容器
- `.topic-popover-mask`: 遮罩层
- `.topic-tabs`: 标签切换
- `.topic-list`: 话题列表
- `.topic-item`: 话题项目

## 使用示例

```vue
<template>
  <Editor 
    :request="request"
    @backData="handleBackData"
  />
</template>

<script>
export default {
  data() {
    return {
      request: {
        getTopic: this.getTopicApi
      }
    }
  },
  methods: {
    async getTopicApi(params) {
      // 实现话题API调用
      return await fetch('/api/topic', {
        method: 'POST',
        body: JSON.stringify(params)
      }).then(res => res.json())
    }
  }
}
</script>
```

## 测试

可以打开 `test-topic.html` 文件来测试话题功能的基本行为。

## 注意事项

1. 确保`request.getTopic`方法已正确实现
2. 话题插入后会自动调用`updateTopicPosition`方法更新位置信息
3. 弹框会自动处理滚动加载和关闭逻辑
4. 话题元素使用`mdd-topic`标签，需要确保样式正确应用
