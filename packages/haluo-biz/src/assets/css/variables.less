/**
 * Created by wa<PERSON><PERSON> on 2017-04-21.
 * 
 */

// color
@brand-color: #FF3C08; // 品牌色（用于多出突出主体的位置) 按钮色值、底部标签栏当前模块色值等(1.3.6)
@assist-color1: #ffa10d; // 辅助色（用于点缀），加精等标签、tab切换跳色值、顶部toast(1.3.6)
@assist-color2: #ff7400; // 辅助色（用于点缀），用于按钮的按压后加深效果(1.3.6)
@orange-color: #ff9600;
@orange-color2: #ffa727;
@orange-color3: #fb8b00;
@one-color: #333; //toast提示图标(1.3.6)
@two-color: #666;
@three-color: #999; // tab 切换未选择颜色，圈子评论文字颜色(1.3.6)
@four-color: #777; // 辅助文字颜色，底部标题栏、输入框、辅助说明（时间、定位、文字标签）文字(1.3.6)
@five-color: #ddd; // 导航栏标题、标题、名称（用户、车辆）、圈子正文(1.3.6)
@six-color: #ccc; // 评论框内名字颜色(1.3.6)、引用内容字体颜色
@base-bg-content: #fff; //内容背景色  白色
@seven-color: #444; //内容背景色  白色
// @base-bg-color: #fafafa;
@base-bg-color: #f5f5f5; // 主背景色
@base-bg-color1: #26262a; // 主背景色 滑@seven-color色值(1.3.6)
@line-color: #e5e5e5; // 分割线(1.3.6 #444) (1.5.0 #1b1b1e)
@footer-bg-color: #1b1b1e; // 底部标签栏底色，用于内容滑动卡片底板，用于分隔条(1.3.6)
@comment-bg-color: #2e2e33; // 圈子评论的底板，车库筛选，车辆卡片的文字部分背板(1.3.6)、详情页未加载完的背景色
@quote-bg-color: #353538; // 引用内容底板，搜索框等底色 、图片未加载完成的背景色（列表）
// size
@one-size: .14rem;
@two-size: .12rem;
@three-size: .1rem;
@four-size: .17rem; //列表标题文字大小
@five-size: .13rem; //列表内容文字大小
// custom
@footer-height: .52rem;