{"name": "@haluo/biz", "description": "rich text", "version": "2.0.38", "type": "module", "module": "./dist/haluo-biz.js", "main": "./dist/haluo-biz.umd.cjs", "exports": {".": {"import": "./dist/haluo-biz.js", "require": "./dist/haluo-biz.umd.cjs"}, "./dist/style.css": "./dist/style.css"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:watch": "vue-tsc --noEmit && vite build --watch", "preview": "vite preview", "pub": "npm publish"}, "dependencies": {"element-plus": "^2.2.28", "uuid": "^9.0.0", "vue": "^3.2.36", "vuedraggable": "^4.1.0"}, "devDependencies": {"@element-plus/icons-vue": "^2.0.10", "@haluo/util": "^1.0.24", "@types/node": "^18.7.18", "@types/uuid": "^9.0.0", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.11", "less": "^4.1.3", "typescript": "^4.6.4", "vite": "^4.0.0", "vite-plugin-dts": "^1.5.0", "vue-tsc": "^0.40.4"}, "peerDependencies": {"vue": "^3.x"}}