
# 说明
线上  tag=online 2.0.x 分支publish
测试  tag=next 1.0.x 分支release
# 发布
```
# nrm use npm
# npm login
# cd ./packages/haluo-biz
# npm run build
npm publish --tag=release # 发布到release环境
npm publish --tag=publish # 发布到publish环境
npm publish # 线上环境
```
# 安装
```
pnpm add @haluo/biz@online
```

# 本地调试
```
1、创建本地库
cd ./packages/haluo-biz
npm link # 在全局 node_modules 目录下创建符号链接，指向当前模块
npm unlink # 删除全局符号链接
npm run build:watch # 监听文件变化，自动编译

2、业务项目根目录中使用软链访问本地库
npm link @haluo/biz # 在项目的 node_modules 目录中创建符号链接，指向全局链接的模块
npm unlink @haluo/biz # 删除项目符号链接

3、查看依赖包（非必需）
npm list # 查看所有依赖包
npm list -g --depth=0 # 查看全局依赖包
```